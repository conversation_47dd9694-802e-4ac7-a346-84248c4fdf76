#!/usr/bin/env python3
"""
JSON HTTP Listener Server

A Python HTTP server utility that continuously listens for incoming HTTP POST requests
containing JSON data and displays them in the console with timestamps.

Usage:
    python json_listener.py [--port PORT] [--host HOST]

Example:
    python json_listener.py --port 8080
    python json_listener.py --host 0.0.0.0 --port 9000
"""

import json
import argparse
import signal
import sys
from datetime import datetime
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse


class JSONRequestHandler(BaseHTTPRequestHandler):
    """HTTP request handler for processing JSON data."""
    
    def log_message(self, format, *args):
        """Override default logging to customize output format."""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] {format % args}")
    
    def do_POST(self):
        """Handle POST requests containing JSON data."""
        try:
            # Get client information
            client_ip = self.client_address[0]
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
            
            # Log connection info
            print(f"\n{'='*60}")
            print(f"[{timestamp}] New POST request from {client_ip}")
            print(f"Path: {self.path}")
            print(f"Headers: {dict(self.headers)}")
            
            # Get content length
            content_length = int(self.headers.get('Content-Length', 0))
            
            if content_length == 0:
                self.send_error_response(400, "No data received")
                print(f"[{timestamp}] ERROR: No data in request body")
                return
            
            # Read the request body
            post_data = self.rfile.read(content_length)
            
            try:
                # Try to decode the data with multiple encodings
                data_str = None
                encoding_used = None

                # List of encodings to try
                encodings = ['utf-8', 'latin-1', 'ascii', 'utf-16', 'cp1252']

                for encoding in encodings:
                    try:
                        data_str = post_data.decode(encoding)
                        encoding_used = encoding
                        break
                    except UnicodeDecodeError:
                        continue

                if data_str is None:
                    # If all encodings fail, show hex dump of first 200 bytes
                    hex_dump = post_data[:200].hex()
                    error_msg = f"Unable to decode data with any common encoding. First 200 bytes (hex): {hex_dump}"
                    self.send_error_response(400, error_msg)
                    print(f"[{timestamp}] ❌ Encoding Error: {error_msg}")
                    print(f"Total data length: {len(post_data)} bytes")
                    return

                print(f"Data decoded using: {encoding_used}")
                print(f"Raw data length: {len(data_str)} characters")

                # Show first 500 characters if data is very long
                if len(data_str) > 1000:
                    print(f"Raw data (first 500 chars): {data_str[:500]}...")
                else:
                    print(f"Raw data: {data_str}")

                # Parse JSON with enhanced error handling
                json_data = None

                # First, try to parse as-is
                try:
                    json_data = json.loads(data_str)
                except json.JSONDecodeError as parse_error:
                    print(f"[{timestamp}] Initial JSON parse failed: {parse_error}")

                    # Analyze the data structure for common issues
                    data_stripped = data_str.strip()

                    # Show beginning and end of data for diagnosis
                    print(f"Data starts with: '{data_stripped[:50]}'")
                    print(f"Data ends with: '{data_stripped[-50:]}'")

                    # Try to fix common truncation issues
                    fixed_data = None

                    # Case 1: Missing opening bracket for array
                    if data_stripped.startswith('"') or data_stripped.startswith(',"'):
                        # Looks like we're missing the opening of a JSON object/array
                        if '{"type":' in data_stripped[:100]:  # Looks like array of objects
                            test_data = '[{"dmac"' + data_stripped
                            try:
                                json_data = json.loads(test_data)
                                fixed_data = test_data
                                print(f"[{timestamp}] ✅ Fixed by adding array opening and object start")
                            except:
                                pass

                    # Case 2: Missing closing bracket
                    if json_data is None and (data_stripped.endswith(',') or not data_stripped.endswith(('}', ']'))):
                        test_data = data_stripped.rstrip(',') + ']'
                        try:
                            json_data = json.loads(test_data)
                            fixed_data = test_data
                            print(f"[{timestamp}] ✅ Fixed by adding closing bracket")
                        except:
                            pass

                    # Case 3: Try wrapping in array brackets
                    if json_data is None:
                        test_data = '[' + data_stripped + ']'
                        try:
                            json_data = json.loads(test_data)
                            fixed_data = test_data
                            print(f"[{timestamp}] ✅ Fixed by wrapping in array brackets")
                        except:
                            pass

                    if json_data is None:
                        # If all fixes failed, raise the original error
                        raise parse_error
                    else:
                        print(f"[{timestamp}] 🔧 JSON was automatically repaired")
                        if len(fixed_data) > 1000:
                            print(f"Fixed data (first 500 chars): {fixed_data[:500]}...")
                        else:
                            print(f"Fixed data: {fixed_data}")

                # Process the JSON data and determine its type
                processed_data = self.process_json_data(json_data, client_ip, timestamp)
                
                # Pretty print the JSON data with message type identification
                msg_type = processed_data.get('message_type', 'unknown')
                print(f"\n📦 Received {msg_type.upper()} Data from {client_ip}:")
                print(json.dumps(json_data, indent=2, ensure_ascii=False))

                # Send success response
                self.send_success_response(json_data)
                print(f"[{timestamp}] ✅ Successfully processed {msg_type} data from {client_ip}")

            except json.JSONDecodeError as e:
                error_msg = f"Invalid JSON format: {str(e)}"
                self.send_error_response(400, error_msg)
                print(f"[{timestamp}] ❌ JSON Parse Error: {error_msg}")
                if data_str:
                    if len(data_str) > 1000:
                        print(f"Raw data received (first 500 chars): {data_str[:500]}...")
                    else:
                        print(f"Raw data received: {data_str}")

            except UnicodeDecodeError as e:
                # This should not happen anymore with the new encoding logic above
                error_msg = f"Unexpected encoding error: {str(e)}"
                self.send_error_response(400, error_msg)
                print(f"[{timestamp}] ❌ Encoding Error: {error_msg}")
                hex_dump = post_data[:200].hex()
                print(f"First 200 bytes (hex): {hex_dump}")
                
        except Exception as e:
            error_msg = f"Server error: {str(e)}"
            self.send_error_response(500, error_msg)
            print(f"[{timestamp}] ❌ Server Error: {error_msg}")
        
        print(f"{'='*60}\n")
    
    def do_GET(self):
        """Handle GET requests with server status."""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        client_ip = self.client_address[0]
        
        print(f"[{timestamp}] GET request from {client_ip} - Path: {self.path}")
        
        if self.path == '/status' or self.path == '/':
            status_info = {
                "status": "running",
                "server": "JSON HTTP Listener",
                "timestamp": timestamp,
                "message": "Server is running and ready to receive JSON data via POST requests"
            }
            
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            
            response_data = json.dumps(status_info, indent=2)
            self.wfile.write(response_data.encode('utf-8'))
            
            print(f"[{timestamp}] ✅ Sent status response to {client_ip}")
        else:
            self.send_error_response(404, "Endpoint not found. Use POST to send JSON data or GET /status for server status.")
    
    def do_OPTIONS(self):
        """Handle OPTIONS requests for CORS preflight."""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'POST, GET, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def send_success_response(self, received_data):
        """Send a success response back to the client."""
        response_data = {
            "status": "success",
            "message": "JSON data received and processed successfully",
            "timestamp": datetime.now().isoformat(),
            "received_data": received_data,
            "processing_info": {
                "server": "Enhanced JSON HTTP Listener",
                "data_type": type(received_data).__name__,
                "keys_count": len(received_data.keys()) if isinstance(received_data, dict) else "N/A"
            }
        }
        
        self.send_response(200)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        response_json = json.dumps(response_data, indent=2)
        self.wfile.write(response_json.encode('utf-8'))
    
    def process_json_data(self, json_data, client_ip, timestamp):
        """Process JSON data and determine its type and content."""
        processed = {
            'original_data': json_data,
            'client_ip': client_ip,
            'timestamp': timestamp
        }
        
        # Determine message type based on common fields
        if isinstance(json_data, dict):
            if 'msg' in json_data and json_data['msg'] == 'alive':
                processed['message_type'] = 'device_alive'
                processed['device_info'] = {
                    'gmac': json_data.get('gmac', 'Unknown'),
                    'version': json_data.get('ver', 'Unknown'),
                    'model': json_data.get('model', 'Unknown'),
                    'memory_free': json_data.get('mem_free', 'Unknown'),
                    'uptime': json_data.get('uptime', 'Unknown')
                }
            elif 'sensor_data' in json_data or any(key in json_data for key in ['temperature', 'humidity', 'pressure', 'light']):
                processed['message_type'] = 'sensor_data'
                processed['sensor_values'] = {
                    'temperature': json_data.get('temperature'),
                    'humidity': json_data.get('humidity'),
                    'pressure': json_data.get('pressure'),
                    'light': json_data.get('light'),
                    'other': {k: v for k, v in json_data.items() if k not in ['temperature', 'humidity', 'pressure', 'light']}
                }
            elif 'beacon_data' in json_data or any(key in json_data for key in ['rssi', 'mac', 'distance', 'uuid']):
                processed['message_type'] = 'beacon_data'
                processed['beacon_values'] = {
                    'rssi': json_data.get('rssi'),
                    'mac': json_data.get('mac'),
                    'distance': json_data.get('distance'),
                    'uuid': json_data.get('uuid'),
                    'other': {k: v for k, v in json_data.items() if k not in ['rssi', 'mac', 'distance', 'uuid']}
                }
            elif 'location' in json_data or any(key in json_data for key in ['lat', 'lng', 'longitude', 'latitude', 'gps']):
                processed['message_type'] = 'location_data'
                processed['location_values'] = {
                    'latitude': json_data.get('lat') or json_data.get('latitude'),
                    'longitude': json_data.get('lng') or json_data.get('longitude'),
                    'timestamp': json_data.get('timestamp', timestamp)
                }
            elif 'command' in json_data:
                processed['message_type'] = 'command_response'
                processed['command_info'] = {
                    'command': json_data.get('command'),
                    'status': json_data.get('status'),
                    'result': json_data.get('result')
                }
            elif 'status' in json_data and isinstance(json_data.get('status'), str):
                processed['message_type'] = 'device_status'
                processed['status_info'] = {
                    'status': json_data.get('status'),
                    'details': json_data.get('details', {})
                }
            else:
                processed['message_type'] = 'custom_data'
                processed['data_keys'] = list(json_data.keys())
        elif isinstance(json_data, list):
            processed['message_type'] = 'array_data'
            processed['array_length'] = len(json_data)
            processed['sample_data'] = json_data[:3] if len(json_data) > 0 else []
        else:
            processed['message_type'] = 'unknown_format'
            processed['data_type'] = type(json_data).__name__
        
        return processed


    def send_error_response(self, status_code, error_message):
        """Send an error response back to the client."""
        response_data = {
            "status": "error",
            "message": error_message,
            "timestamp": datetime.now().isoformat()
        }
        
        self.send_response(status_code)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        response_json = json.dumps(response_data, indent=2)
        self.wfile.write(response_json.encode('utf-8'))


class JSONListenerServer:
    """Main server class for the JSON HTTP Listener."""
    
    def __init__(self, host='************', port=8080):
        self.host = host
        self.port = port
        self.server = None
    
    def start(self):
        """Start the HTTP server."""
        try:
            self.server = HTTPServer((self.host, self.port), JSONRequestHandler)
            
            print(f"🚀 Enhanced JSON HTTP Listener Server Starting...")
            print(f"📡 Listening on {self.host}:{self.port}")
            print(f"🔗 Server URL: http://{self.host}:{self.port}")
            print(f"📋 Status endpoint: http://{self.host}:{self.port}/status")
            print(f"📝 Send POST requests with JSON data to receive and display them")
            print(f"🔍 Server enhanced to process multiple data types: sensor, beacon, location, device status, etc.")
            print(f"⏹️  Press Ctrl+C to stop the server")
            print(f"{'='*60}\n")
            
            # Start serving requests
            self.server.serve_forever()
            
        except OSError as e:
            if e.errno == 98 or "Address already in use" in str(e):
                print(f"❌ Error: Port {self.port} is already in use.")
                print(f"💡 Try using a different port with --port option")
            else:
                print(f"❌ Error starting server: {e}")
            sys.exit(1)
        except KeyboardInterrupt:
            self.stop()
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
            sys.exit(1)
    
    def stop(self):
        """Stop the HTTP server gracefully."""
        if self.server:
            print(f"\n🛑 Shutting down server...")
            self.server.shutdown()
            self.server.server_close()
            print(f"✅ Server stopped successfully")

def signal_handler(signum, frame):
    """Handle interrupt signals gracefully."""
    print(f"\n🛑 Received interrupt signal. Shutting down...")
    sys.exit(0)


def main():
    """Main function to parse arguments and start the server."""
    parser = argparse.ArgumentParser(
        description="JSON HTTP Listener Server - Continuously listens for JSON data via HTTP POST requests",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python json_listener.py                    # Start on ************:8080
  python json_listener.py --port 9000        # Start on ************:9000
  python json_listener.py --host 0.0.0.0     # Listen on all interfaces
  python json_listener.py --host 0.0.0.0 --port 8888  # Custom host and port

Test the server:
  curl -X POST http://************:8080 -H "Content-Type: application/json" -d '{"test": "data"}'
  curl http://************:8080/status
        """
    )
    
    parser.add_argument(
        '--host',
        default='************',
        help='Host to bind the server to (default: ************)'
    )
    
    parser.add_argument(
        '--port',
        type=int,
        default=8080,
        help='Port to listen on (default: 8080)'
    )
    
    args = parser.parse_args()
    
    # Set up signal handlers for graceful shutdown
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Create and start the server
    server = JSONListenerServer(host=args.host, port=args.port)
    server.start()


if __name__ == '__main__':
    main()