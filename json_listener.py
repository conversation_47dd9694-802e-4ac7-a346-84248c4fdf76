#!/usr/bin/env python3
"""
JSON HTTP Listener Server

A Python HTTP server utility that continuously listens for incoming HTTP POST requests
containing JSON data and displays them in the console with timestamps.

Usage:
    python json_listener.py [--port PORT] [--host HOST]

Example:
    python json_listener.py --port 8080
    python json_listener.py --host 0.0.0.0 --port 9000
"""

import json
import argparse
import signal
import sys
from datetime import datetime
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse


class JSONRequestHandler(BaseHTTPRequestHandler):
    """HTTP request handler for processing JSON data."""
    
    def log_message(self, format, *args):
        """Override default logging to customize output format."""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] {format % args}")
    
    def do_POST(self):
        """Handle POST requests containing JSON data."""
        try:
            # Get client information
            client_ip = self.client_address[0]
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
            
            # Log connection info
            print(f"\n{'='*60}")
            print(f"[{timestamp}] New POST request from {client_ip}")
            print(f"Path: {self.path}")
            print(f"Headers: {dict(self.headers)}")
            
            # Get content length
            content_length = int(self.headers.get('Content-Length', 0))
            
            if content_length == 0:
                self.send_error_response(400, "No data received")
                print(f"[{timestamp}] ERROR: No data in request body")
                return
            
            # Read the request body
            post_data = self.rfile.read(content_length)
            
            try:
                # Decode the data
                data_str = post_data.decode('utf-8')
                print(f"Raw data: {data_str}")
                
                # Parse JSON
                json_data = json.loads(data_str)
                
                # Pretty print the JSON data
                print(f"\n📦 Received JSON Data:")
                print(json.dumps(json_data, indent=2, ensure_ascii=False))
                
                # Send success response
                self.send_success_response(json_data)
                print(f"[{timestamp}] ✅ Successfully processed JSON data")
                
            except json.JSONDecodeError as e:
                error_msg = f"Invalid JSON format: {str(e)}"
                self.send_error_response(400, error_msg)
                print(f"[{timestamp}] ❌ JSON Parse Error: {error_msg}")
                print(f"Raw data received: {data_str}")
                
            except UnicodeDecodeError as e:
                error_msg = f"Unable to decode data as UTF-8: {str(e)}"
                self.send_error_response(400, error_msg)
                print(f"[{timestamp}] ❌ Encoding Error: {error_msg}")
                
        except Exception as e:
            error_msg = f"Server error: {str(e)}"
            self.send_error_response(500, error_msg)
            print(f"[{timestamp}] ❌ Server Error: {error_msg}")
        
        print(f"{'='*60}\n")
    
    def do_GET(self):
        """Handle GET requests with server status."""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        client_ip = self.client_address[0]
        
        print(f"[{timestamp}] GET request from {client_ip} - Path: {self.path}")
        
        if self.path == '/status' or self.path == '/':
            status_info = {
                "status": "running",
                "server": "JSON HTTP Listener",
                "timestamp": timestamp,
                "message": "Server is running and ready to receive JSON data via POST requests"
            }
            
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            
            response_data = json.dumps(status_info, indent=2)
            self.wfile.write(response_data.encode('utf-8'))
            
            print(f"[{timestamp}] ✅ Sent status response to {client_ip}")
        else:
            self.send_error_response(404, "Endpoint not found. Use POST to send JSON data or GET /status for server status.")
    
    def do_OPTIONS(self):
        """Handle OPTIONS requests for CORS preflight."""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'POST, GET, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def send_success_response(self, received_data):
        """Send a success response back to the client."""
        response_data = {
            "status": "success",
            "message": "JSON data received successfully",
            "timestamp": datetime.now().isoformat(),
            "received_data": received_data
        }
        
        self.send_response(200)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        response_json = json.dumps(response_data, indent=2)
        self.wfile.write(response_json.encode('utf-8'))
    
    def send_error_response(self, status_code, error_message):
        """Send an error response back to the client."""
        response_data = {
            "status": "error",
            "message": error_message,
            "timestamp": datetime.now().isoformat()
        }
        
        self.send_response(status_code)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        response_json = json.dumps(response_data, indent=2)
        self.wfile.write(response_json.encode('utf-8'))


class JSONListenerServer:
    """Main server class for the JSON HTTP Listener."""
    
    def __init__(self, host='localhost', port=8080):
        self.host = host
        self.port = port
        self.server = None
    
    def start(self):
        """Start the HTTP server."""
        try:
            self.server = HTTPServer((self.host, self.port), JSONRequestHandler)
            
            print(f"🚀 JSON HTTP Listener Server Starting...")
            print(f"📡 Listening on {self.host}:{self.port}")
            print(f"🔗 Server URL: http://{self.host}:{self.port}")
            print(f"📋 Status endpoint: http://{self.host}:{self.port}/status")
            print(f"📝 Send POST requests with JSON data to receive and display them")
            print(f"⏹️  Press Ctrl+C to stop the server")
            print(f"{'='*60}\n")
            
            # Start serving requests
            self.server.serve_forever()
            
        except OSError as e:
            if e.errno == 98 or "Address already in use" in str(e):
                print(f"❌ Error: Port {self.port} is already in use.")
                print(f"💡 Try using a different port with --port option")
            else:
                print(f"❌ Error starting server: {e}")
            sys.exit(1)
        except KeyboardInterrupt:
            self.stop()
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
            sys.exit(1)
    
    def stop(self):
        """Stop the HTTP server gracefully."""
        if self.server:
            print(f"\n🛑 Shutting down server...")
            self.server.shutdown()
            self.server.server_close()
            print(f"✅ Server stopped successfully")


def signal_handler(signum, frame):
    """Handle interrupt signals gracefully."""
    print(f"\n🛑 Received interrupt signal. Shutting down...")
    sys.exit(0)


def main():
    """Main function to parse arguments and start the server."""
    parser = argparse.ArgumentParser(
        description="JSON HTTP Listener Server - Continuously listens for JSON data via HTTP POST requests",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python json_listener.py                    # Start on localhost:8080
  python json_listener.py --port 9000        # Start on localhost:9000
  python json_listener.py --host 0.0.0.0     # Listen on all interfaces
  python json_listener.py --host 0.0.0.0 --port 8888  # Custom host and port

Test the server:
  curl -X POST http://localhost:8080 -H "Content-Type: application/json" -d '{"test": "data"}'
  curl http://localhost:8080/status
        """
    )
    
    parser.add_argument(
        '--host',
        default='localhost',
        help='Host to bind the server to (default: localhost)'
    )
    
    parser.add_argument(
        '--port',
        type=int,
        default=8080,
        help='Port to listen on (default: 8080)'
    )
    
    args = parser.parse_args()
    
    # Set up signal handlers for graceful shutdown
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Create and start the server
    server = JSONListenerServer(host=args.host, port=args.port)
    server.start()


if __name__ == '__main__':
    main()
